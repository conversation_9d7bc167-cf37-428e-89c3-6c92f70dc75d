// Import all the theming functionality.
@use '../core-theme';
@use '../../autocomplete/autocomplete-theme';
@use '../../badge/badge-theme';
@use '../../bottom-sheet/bottom-sheet-theme';
@use '../../button/button-theme';
@use '../../button/icon-button-theme';
@use '../../button/fab-theme';
@use '../../button-toggle/button-toggle-theme';
@use '../../card/card-theme';
@use '../../checkbox/checkbox-theme';
@use '../../chips/chips-theme';
@use '../../table/table-theme';
@use '../../datepicker/datepicker-theme';
@use '../../dialog/dialog-theme';
@use '../../divider/divider-theme';
@use '../../expansion/expansion-theme';
@use '../../grid-list/grid-list-theme';
@use '../../icon/icon-theme';
@use '../../input/input-theme';
@use '../../list/list-theme';
@use '../../menu/menu-theme';
@use '../../paginator/paginator-theme';
@use '../../progress-bar/progress-bar-theme';
@use '../../progress-spinner/progress-spinner-theme';
@use '../../radio/radio-theme';
@use '../../select/select-theme';
@use '../../sidenav/sidenav-theme';
@use '../../slide-toggle/slide-toggle-theme';
@use '../../slider/slider-theme';
@use '../../stepper/stepper-theme';
@use '../../sort/sort-theme';
@use '../../tabs/tabs-theme';
@use '../../toolbar/toolbar-theme';
@use '../../tooltip/tooltip-theme';
@use '../../tree/tree-theme';
@use '../../snack-bar/snack-bar-theme';
@use '../../form-field/form-field-theme';
@use './theming';

// Create a theme.
@mixin all-component-themes($theme-or-color-config) {
  $dedupe-key: 'angular-material-theme';
  @include theming.private-check-duplicate-theme-styles($theme-or-color-config, $dedupe-key) {
    @include core-theme.theme($theme-or-color-config);
    @include card-theme.theme($theme-or-color-config);
    @include progress-bar-theme.theme($theme-or-color-config);
    @include tooltip-theme.theme($theme-or-color-config);
    @include form-field-theme.theme($theme-or-color-config);
    @include input-theme.theme($theme-or-color-config);
    @include select-theme.theme($theme-or-color-config);
    @include autocomplete-theme.theme($theme-or-color-config);
    @include dialog-theme.theme($theme-or-color-config);
    @include chips-theme.theme($theme-or-color-config);
    @include slide-toggle-theme.theme($theme-or-color-config);
    @include radio-theme.theme($theme-or-color-config);
    @include slider-theme.theme($theme-or-color-config);
    @include menu-theme.theme($theme-or-color-config);
    @include list-theme.theme($theme-or-color-config);
    @include paginator-theme.theme($theme-or-color-config);
    @include tabs-theme.theme($theme-or-color-config);
    @include checkbox-theme.theme($theme-or-color-config);
    @include button-theme.theme($theme-or-color-config);
    @include icon-button-theme.theme($theme-or-color-config);
    @include fab-theme.theme($theme-or-color-config);
    @include snack-bar-theme.theme($theme-or-color-config);
    @include table-theme.theme($theme-or-color-config);
    @include progress-spinner-theme.theme($theme-or-color-config);
    @include badge-theme.theme($theme-or-color-config);
    @include bottom-sheet-theme.theme($theme-or-color-config);
    @include button-toggle-theme.theme($theme-or-color-config);
    @include datepicker-theme.theme($theme-or-color-config);
    @include divider-theme.theme($theme-or-color-config);
    @include expansion-theme.theme($theme-or-color-config);
    @include grid-list-theme.theme($theme-or-color-config);
    @include icon-theme.theme($theme-or-color-config);
    @include sidenav-theme.theme($theme-or-color-config);
    @include stepper-theme.theme($theme-or-color-config);
    @include sort-theme.theme($theme-or-color-config);
    @include toolbar-theme.theme($theme-or-color-config);
    @include tree-theme.theme($theme-or-color-config);
  }
}

// @deprecated Use `all-component-themes`.
@mixin angular-material-theme($theme-or-color-config) {
  @include all-component-themes($theme-or-color-config);
}
