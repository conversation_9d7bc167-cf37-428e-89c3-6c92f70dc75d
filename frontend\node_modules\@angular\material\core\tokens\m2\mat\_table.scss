@use 'sass:map';
@use '../../../theming/theming';
@use '../../../typography/typography-utils';
@use '../../token-utils';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, table);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    row-item-outline-width: 1px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  $foreground: map.get($config, foreground);
  $background: map.get($config, background);

  @return (
    background-color: theming.get-color-from-palette($background, 'card'),

    header-headline-color: theming.get-color-from-palette($foreground, text),
    row-item-label-text-color: theming.get-color-from-palette($foreground, text),
    row-item-outline-color: theming.get-color-from-palette($foreground, divider),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  $fallback-font: typography-utils.font-family($config);
  $cell-font-family: typography-utils.font-family($config, body-2) or $fallback-font;
  $cell-line-height: typography-utils.line-height($config, body-2);
  $cell-font-size: typography-utils.font-size($config, body-2);
  $cell-font-weight: typography-utils.font-weight($config, body-2);
  $cell-letter-spacing: typography-utils.letter-spacing($config, body-2);

  @return (
    header-headline-font: typography-utils.font-family($config, subtitle-2) or $fallback-font,
    header-headline-line-height: typography-utils.line-height($config, subtitle-2),
    header-headline-size: typography-utils.font-size($config, subtitle-2),
    header-headline-weight: typography-utils.font-weight($config, subtitle-2),
    header-headline-tracking: typography-utils.letter-spacing($config, subtitle-2),

    // Plain cells and footer cells have the same typography.
    row-item-label-text-font: $cell-font-family,
    row-item-label-text-line-height: $cell-line-height,
    row-item-label-text-size: $cell-font-size,
    row-item-label-text-weight: $cell-font-weight,
    row-item-label-text-tracking: $cell-letter-spacing,

    footer-supporting-text-font: $cell-font-family,
    footer-supporting-text-line-height: $cell-line-height,
    footer-supporting-text-size: $cell-font-size,
    footer-supporting-text-weight: $cell-font-weight,
    footer-supporting-text-tracking: $cell-letter-spacing,
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  $scale: theming.clamp-density($config, -4);
  $header-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 44px,
    -4: 40px
  );
  $cell-scale: (
    0: 52px,
    -1: 48px,
    -2: 44px,
    -3: 40px,
    -4: 36px
  );

  @return (
    header-container-height: map.get($header-scale, $scale),
    footer-container-height: map.get($cell-scale, $scale),
    row-item-container-height: map.get($cell-scale, $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
          get-unthemable-tokens(),
          get-color-tokens(token-utils.$placeholder-color-config),
          get-typography-tokens(token-utils.$placeholder-typography-config),
          get-density-tokens(token-utils.$placeholder-density-config)
  );
}
