@use 'sass:map';
@use '../core/theming/theming';
@use '../core/style/private';
@use '../core/style/form-common';
@use '../core/typography/typography';
@use '../core/typography/typography-utils';


/// @deprecated Use `mat.select-color` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);
  $foreground: map.get($config, foreground);
  $background: map.get($config, background);
  $primary: map.get($config, primary);
  $accent: map.get($config, accent);
  $warn: map.get($config, warn);

  .mat-select-value {
    color: theming.get-color-from-palette($foreground, text);
  }

  .mat-select-placeholder {
    color: form-common.private-control-placeholder-color($config);
  }

  .mat-select-disabled .mat-select-value {
    color: theming.get-color-from-palette($foreground, disabled-text);
  }

  .mat-select-arrow {
    color: theming.get-color-from-palette($foreground, secondary-text);
  }

  .mat-select-panel {
    background: theming.get-color-from-palette($background, card);
    @include private.private-theme-overridable-elevation(4, $config);

    .mat-option.mat-selected:not(.mat-option-multiple) {
      background: theming.get-color-from-palette($background, hover, 0.12);
    }
  }

  .mat-form-field {
    &.mat-focused {
      &.mat-primary .mat-select-arrow {
        color: theming.get-color-from-palette($primary, text);
      }

      &.mat-accent .mat-select-arrow {
        color: theming.get-color-from-palette($accent, text);
      }

      &.mat-warn .mat-select-arrow {
        color: theming.get-color-from-palette($warn, text);
      }
    }

    .mat-select.mat-select-invalid .mat-select-arrow {
      color: theming.get-color-from-palette($warn, text);
    }

    .mat-select.mat-select-disabled .mat-select-arrow {
      color: theming.get-color-from-palette($foreground, disabled-text);
    }
  }
}

/// @deprecated Use `mat.select-typography` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin typography($config-or-theme) {
  $config: typography.private-typography-to-2014-config(
      theming.get-typography-config($config-or-theme));
  // The unit-less line-height from the font config.
  $line-height: typography-utils.line-height($config, input);

  .mat-select {
    font-family: typography-utils.font-family($config);
  }

  .mat-select-trigger {
    height: typography-utils.private-coerce-unitless-to-em($line-height);
  }
}

@mixin _density($config-or-theme) {}

/// @deprecated Use `mat.select-theme` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-legacy-select') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include _density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
