@use 'sass:map';
@use '@material/density' as mdc-density;
@use '@material/textfield' as mdc-textfield;
@use '../core/tokens/m2/mat/select' as tokens-mat-select;
@use '../core/tokens/token-utils';
@use '../core/style/sass-utils';

@use '../core/theming/theming';
@use '../core/typography/typography';

@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-select.$prefix,
      tokens-mat-select.get-color-tokens($config));

    .mat-mdc-form-field.mat-accent {
      $accent: map.get($config, accent);
      $accent-config: map.merge($config, (primary: $accent));
      @include token-utils.create-token-values(tokens-mat-select.$prefix,
        tokens-mat-select.get-color-tokens($accent-config));
    }

    .mat-mdc-form-field.mat-warn {
      $warn: map.get($config, warn);
      $warn-config: map.merge($config, (primary: $warn));
      @include token-utils.create-token-values(tokens-mat-select.$prefix,
        tokens-mat-select.get-color-tokens($warn-config));
    }
  }
}

@mixin typography($config-or-theme) {
  $config: typography.private-typography-to-2018-config(
      theming.get-typography-config($config-or-theme));

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-select.$prefix,
      tokens-mat-select.get-typography-tokens($config));
  }
}

@mixin density($config-or-theme) {
  $density-scale: theming.get-density-config($config-or-theme);

  // Density is clamped at -5 here, because MDC's form field throws an error for anything lower.
  $form-field-height: mdc-density.prop-value(
    $density-config: mdc-textfield.$density-config,
    $density-scale: theming.clamp-density($density-scale, -5),
    $property-name: height,
  );

  // On lower densities the filled form field hides its label which causes the label to
  // be misaligned. Remove the additional offset that was added because of the label.
  @if ($form-field-height < mdc-textfield.$minimum-height-for-filled-label) {
    .mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper {
      transform: none;
    }
  }

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-select.$prefix,
      tokens-mat-select.get-density-tokens($density-scale));
  }
}

@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-select') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
