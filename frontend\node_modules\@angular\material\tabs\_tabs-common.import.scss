@forward '../../material/core/style/private.import';
@forward '../../material/core/style/vendor-prefixes.import';
@forward '../../cdk/a11y/index.import';
@forward 'tabs-common' hide paginated-tab-header, paginated-tab-header-container,
paginated-tab-header-item-wrapper, tab, tab-ripple;
@forward 'tabs-common' as mat-mdc-* hide $mat-mdc-mat-tab-animation-duration;

@import '../../material/core/style/variables';
@import '../../material/core/style/private';
@import '../../material/core/style/vendor-prefixes';
@import '../../cdk/a11y';
