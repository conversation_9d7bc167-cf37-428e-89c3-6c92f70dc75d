@use 'sass:map';
@use '../../token-utils';
@use '../../../typography/typography-utils';
@use '../../../theming/theming';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, toolbar);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  $foreground: map.get($config, foreground);
  $background: map.get($config, background);

  @return private-get-color-palette-color-tokens(
    $background-color: theming.get-color-from-palette($background, app-bar),
    $text-color: theming.get-color-from-palette($foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  @return (
    title-text-font: typography-utils.font-family($config, title) or
      typography-utils.font-family($config),
    title-text-line-height: typography-utils.line-height($config, title),
    title-text-size: typography-utils.font-size($config, title),
    title-text-tracking: typography-utils.letter-spacing($config, title),
    title-text-weight: typography-utils.font-weight($config, title),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  $density-scale: theming.clamp-density($config, -3);
  $standard-scale: (
    0: 64px,
    -1: 60px,
    -2: 56px,
    -3: 52px,
  );

  $mobile-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 44px,
  );

  @return (
    standard-height: map.get($standard-scale, $density-scale),
    mobile-height: map.get($mobile-scale, $density-scale),
  );
}

// Generates the tokens used to theme the toolbar based on a palette.
@function private-get-color-palette-color-tokens($background-color, $text-color) {
  @return (
    container-background-color: $background-color,
    container-text-color: $text-color,
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
