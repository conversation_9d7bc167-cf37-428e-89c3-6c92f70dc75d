@use 'sass:map';
@use '../core/theming/theming';
@use '../core/typography/typography';
@use '../core/typography/typography-utils';

/// @deprecated Use `mat.table-color` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);
  $background: map.get($config, background);
  $foreground: map.get($config, foreground);

  .mat-table {
    background: theming.get-color-from-palette($background, 'card');
  }

  .mat-table thead, .mat-table tbody, .mat-table tfoot,
  mat-header-row, mat-row, mat-footer-row,
  [mat-header-row], [mat-row], [mat-footer-row],
  .mat-table-sticky {
    background: inherit;
  }

  mat-row, mat-header-row, mat-footer-row,
  th.mat-header-cell, td.mat-cell, td.mat-footer-cell {
    border-bottom-color: theming.get-color-from-palette($foreground, divider);
  }

  .mat-header-cell {
    color: theming.get-color-from-palette($foreground, secondary-text);
  }

  .mat-cell, .mat-footer-cell {
    color: theming.get-color-from-palette($foreground, text);
  }
}

/// @deprecated Use `mat.table-typography` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin typography($config-or-theme) {
  $config: typography.private-typography-to-2014-config(
      theming.get-typography-config($config-or-theme));
  .mat-table {
    font-family: typography-utils.font-family($config);
  }

  .mat-header-cell {
    font-size: typography-utils.font-size($config, caption);
    font-weight: typography-utils.font-weight($config, body-2);
  }

  .mat-cell, .mat-footer-cell {
    font-size: typography-utils.font-size($config, body-1);
  }
}

@mixin _density($config-or-theme) {}

/// @deprecated Use `mat.table-theme` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-legacy-table') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include _density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
