/* chat.component.scss - Your original styles restored */

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px 40px 140px 40px;
  background: transparent;
  max-height: calc(100vh - 200px);
  scroll-behavior: smooth;
  position: relative;
  z-index: 10; /* Ensures proper stacking order */

  /* Enhanced scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(241, 241, 241, 0.5);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #c1c1c1 0%, #a8a8a8 100%);
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #a8a8a8 0%, #9ca3af 100%);
    }
  }
}

/* Pagination loading spinner - simple transparent version */
.pagination-loading {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-bottom: 20px;
  
  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(55, 65, 81, 0.1);
    border-top: 3px solid #374151;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.message {
  margin-bottom: 20px;
  display: flex;
  animation: slideInUp 0.3s ease-out;
  position: relative;
 
  &.user-message {
    justify-content: flex-end;
 
    .message-content {
      background: #e5e5ea;
      color: #000;
      max-width: 75%;
      border-radius: 18px 18px 4px 18px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      transform: translateY(0);
      transition: all 0.2s ease;
      position: relative;
 
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
 
  &.bot-message {
    justify-content: flex-start;
 
    .message-content {
      background: #fff;
      color: #000;
      max-width: 75%;
      border-radius: 18px 18px 18px 4px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      transform: translateY(0);
      transition: all 0.2s ease;
      position: relative;
      border: 1px solid rgba(224, 224, 224, 0.5);
 
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.message-content {
  padding: 16px 20px;
  word-wrap: break-word;
  position: relative;
}

.message-text {
  font-size: 15px;
  line-height: 1.5;
  white-space: pre-wrap;
  margin: 0;
  font-weight: 400;
}

.message-time {
  font-size: 11px;
  color: #8e8e93;
  margin-top: 8px;
  text-align: right;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: flex-end;
}

.bot-message .message-time {
  text-align: left;
  justify-content: flex-start;
}

.message:hover .message-time {
  opacity: 1;
}

.input-container {
  padding: 24px 40px 32px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 800px;
  z-index: 50; /* Reduced from 100 to be under messages when needed */
  border-top: 1px solid rgba(224, 224, 224, 0.5);
  transition: all 0.3s ease;

  &.centered {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 800px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 50;
    padding: 32px 40px;
    bottom: auto;
    border-radius: 20px;
    border: 1px solid rgba(224, 224, 224, 0.5);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  &.bottom {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    border-top: 1px solid rgba(224, 224, 224, 0.5);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    width: 100%;
    max-width: 800px;
    z-index: 50;
  }
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  border: 2px solid rgba(209, 213, 219, 0.8);
  border-radius: 28px;
  padding: 6px 8px 6px 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  &:focus-within {
    border-color: rgba(156, 163, 175, 0.8);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, transparent 0%, rgba(156, 163, 175, 0.1) 50%, transparent 100%);
    border-radius: 30px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:focus-within::before {
    opacity: 1;
  }
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: 16px;
  line-height: 1.5;
  padding: 14px 0;
  min-height: 24px;
  max-height: 120px;
  font-family: inherit;
  overflow-y: auto;
  transition: all 0.3s ease;
  color: #374151;

  &::placeholder {
    color: #9ca3af;
    font-weight: 400;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Enhanced scrollbar for textarea */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
    border-radius: 4px;
    transition: background 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    }
  }
}

.send-button {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  flex-shrink: 0;
  margin: 2px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 12px rgba(55, 65, 81, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
  }

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(55, 65, 81, 0.4);

    &::before {
      width: 100%;
      height: 100%;
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0) scale(1);
    box-shadow: 0 2px 8px rgba(55, 65, 81, 0.3);
  }

  &:disabled {
    background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  svg {
    width: 18px;
    height: 18px;
    transition: transform 0.3s ease;
    z-index: 1;
  }

  &:hover:not(:disabled) svg {
    transform: scale(1.1) rotate(5deg);
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;

  span {
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

/* Auth loading styles */
.auth-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #374151;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  p {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
  }
}

/* Enhanced animations */
@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(0.8) translateY(0);
  }
  30% {
    opacity: 1;
    transform: scale(1.2) translateY(-4px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsive enhancements */
@media (max-width: 768px) {
  .chat-container {
    max-width: 100%;
    height: 100vh;
  }

  .messages-container {
    padding: 20px 20px 140px 20px;
  }

  .input-container {
    padding: 20px 20px 24px 20px;
    max-width: 100%;

    &.centered {
      max-width: 100%;
      padding: 20px;
      margin: 0 16px;
      width: calc(100% - 32px);
    }
  }

  .message {
    margin-bottom: 20px;

    &.user-message .message-content,
    &.bot-message .message-content {
      max-width: 85%;
    }
  }

  .input-wrapper {
    padding: 4px 6px 4px 16px;
    gap: 8px;
  }

  .send-button {
    width: 36px;
    height: 36px;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}

/* Smooth transitions for better UX */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Focus indicators for accessibility */
.send-button:focus {
  outline: 2px solid #374151;
  outline-offset: 2px;
}

.message-input:focus {
  box-shadow: none;
}