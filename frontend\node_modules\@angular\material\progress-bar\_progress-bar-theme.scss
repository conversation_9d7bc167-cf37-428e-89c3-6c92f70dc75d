@use '../core/theming/theming';
@use '../core/tokens/m2/mdc/linear-progress' as tokens-mdc-linear-progress;
@use '@material/linear-progress/linear-progress-theme' as mdc-linear-progress-theme;
@use 'sass:map';

@mixin _palette-styles($config, $palette-name) {
  $palette-config: map.merge($config, (primary: map.get($config, $palette-name)));
  $color-tokens: tokens-mdc-linear-progress.get-color-tokens($palette-config);

  // We can't set the `track-color` using `theme`, because it isn't possible for it to use a CSS
  // variable since MDC's buffer animation works by constructing an SVG string from this color.
  // We also can't set the `track-color` by creating our own token slot because the track-color
  // is set in the SVG string in `theme-styles`.
  @include mdc-linear-progress-theme.theme-styles((
    // TODO(crisbeto): the buffer color should come from somewhere in MDC, however at the time of
    // writing, their buffer color is hardcoded to #e6e6e6 which both doesn't account for theming
    // and doesn't match the Material design spec. For now we approximate the buffer background by
    // applying an opacity to the color of the bar.
    track-color: map.get($color-tokens, track-color),
  ));

  @include mdc-linear-progress-theme.theme($color-tokens);
}

@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);

  .mat-mdc-progress-bar {
    @include _palette-styles($config, primary);

    &.mat-accent {
      @include _palette-styles($config, accent);
    }

    &.mat-warn {
      @include _palette-styles($config, warn);
    }
  }
}

@mixin typography($config-or-theme) {}

@mixin density($config-or-theme) {}

@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-progress-bar') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
