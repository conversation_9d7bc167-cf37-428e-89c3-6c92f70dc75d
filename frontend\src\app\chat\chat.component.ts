import { Component, OnInit, ElementRef, ViewChild, AfterViewChecked } from '@angular/core';
import { AuthService } from '../auth.service'; // Adjust path as needed

interface Message {
  id?: number;
  text: string;
  isUser: boolean;
  timestamp: Date;
  session?: number;
  prompt?: number;
  model?: number;
}

// Django ChatMessage structure
interface ChatMessage {
  id: number;
  session: number;
  sender: 'user' | 'bot';
  message: string;
  timestamp: string;
  prompt: number | null;
  model: number | null;
}

// Django paginated response
interface ChatHistoryResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ChatMessage[];
}

// Django chatbot response
interface ChatbotResponse {
  user_message: ChatMessage;
  bot_message: ChatMessage;
}

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, AfterViewChecked {
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  
  messages: Message[] = [];
  currentMessage: string = '';
  isLoading: boolean = false;
  isAuthenticated: boolean = false;
  currentSessionId: number | null = null;
  
  // Pagination
  currentPage: number = 1;
  hasNextPage: boolean = false;
  isLoadingHistory: boolean = false;
  
  // Scroll management
  private shouldScrollToBottom: boolean = true;
  private lastScrollHeight: number = 0;

  constructor(private authService: AuthService) {}

  ngOnInit() {
    // Ensure user is authenticated before initializing chat
    this.authService.ensureAuthenticated().subscribe({
      next: (token) => {
        console.log('User authenticated successfully');
        this.isAuthenticated = true;
        this.loadChatHistory();
      },
      error: (error) => {
        console.error('Authentication failed:', error);
        this.isAuthenticated = false;
      }
    });
  }

  ngAfterViewChecked() {
    // Auto-scroll to bottom only for new messages
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  // Listen for scroll events to load more history
  onScroll(event: any) {
    const element = event.target;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    
    // If user scrolled to within 100px of the top, load more history
    if (scrollTop < 100 && this.hasNextPage && !this.isLoadingHistory) {
      this.lastScrollHeight = scrollHeight;
      this.loadMoreHistory();
    }
  }

  loadChatHistory(page: number = 1, append: boolean = false) {
    if (!this.isAuthenticated) return;

    this.isLoadingHistory = true;

    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({
      next: (response: ChatHistoryResponse) => {
        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));
        
        if (append) {
          // For pagination - prepend older messages to beginning
          this.messages = [...newMessages, ...this.messages];
          this.maintainScrollPosition();
        } else {
          // For initial load - replace all messages (already in chronological order)
          this.messages = newMessages;
          this.shouldScrollToBottom = true;
        }
        
        // Update pagination info
        this.currentPage = page;
        this.hasNextPage = !!response.next;
        
        this.isLoadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading chat history:', error);
        this.isLoadingHistory = false;
        // Continue with existing messages
      }
    });
  }

  // Convert Django ChatMessage to frontend Message format
  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {
    return {
      id: chatMessage.id,
      text: chatMessage.message,
      isUser: chatMessage.sender === 'user',
      timestamp: new Date(chatMessage.timestamp),
      session: chatMessage.session,
      prompt: chatMessage.prompt || undefined,
      model: chatMessage.model || undefined
    };
  }

  // Load more chat history (pagination) - triggered by scroll
  loadMoreHistory() {
    if (this.hasNextPage && !this.isLoadingHistory) {
      this.loadChatHistory(this.currentPage + 1, true);
    }
  }

  // Maintain scroll position when loading older messages
  private maintainScrollPosition() {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        const newScrollHeight = element.scrollHeight;
        const scrollDifference = newScrollHeight - this.lastScrollHeight;
        element.scrollTop = scrollDifference;
      }
    }, 50);
  }

  sendMessage() {
    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {
      return;
    }

    // Store the message to send
    const messageToSend = this.currentMessage;
    this.currentMessage = '';
    this.isLoading = true;
    
    // Set flag to scroll to bottom after new messages
    this.shouldScrollToBottom = true;

    // Call the API through auth service
    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({
      next: (response: ChatbotResponse) => {
        // Convert and add user message
        const userMessage = this.convertChatMessageToMessage(response.user_message);
        const botMessage = this.convertChatMessageToMessage(response.bot_message);
        
        // Add both messages to the end of chat (chronological order)
        this.messages.push(userMessage);
        this.messages.push(botMessage);
        
        // Store session ID for future requests
        if (!this.currentSessionId) {
          this.currentSessionId = response.user_message.session;
        }
        
        this.isLoading = false;
        this.shouldScrollToBottom = true;
      },
      error: (error) => {
        console.error('Error sending message:', error);
        const errorMessage: Message = {
          text: 'Sorry, there was an error processing your message. Please try again.',
          isUser: false,
          timestamp: new Date()
        };
        this.messages.push(errorMessage);
        this.isLoading = false;
        this.shouldScrollToBottom = true;
      }
    });
  }

  clearHistory() {
    this.messages = [];
  }

  refreshHistory() {
    this.currentPage = 1;
    this.loadChatHistory();
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  adjustTextareaHeight(event: any) {
    const textarea = event.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  }

  private scrollToBottom() {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 50);
  }
}