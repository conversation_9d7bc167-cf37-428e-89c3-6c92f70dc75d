@use 'sass:map';
@use '../../token-utils';
@use '../../../typography/typography-utils';
@use '../../../theming/theming';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, bottom-sheet);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // TODO: will be necessary for M3.
    container-shape: 4px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  $foreground: map.get($config, foreground);
  $background: map.get($config, background);

  @return (
    container-text-color: theming.get-color-from-palette($foreground, text),
    container-background-color: theming.get-color-from-palette($background, dialog),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  @return (
    container-text-font: typography-utils.font-family($config, body-1) or
      typography-utils.font-family($config),
    container-text-line-height: typography-utils.line-height($config, body-1),
    container-text-size: typography-utils.font-size($config, body-1),
    container-text-tracking: typography-utils.letter-spacing($config, body-1),
    container-text-weight: typography-utils.font-weight($config, body-1),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
