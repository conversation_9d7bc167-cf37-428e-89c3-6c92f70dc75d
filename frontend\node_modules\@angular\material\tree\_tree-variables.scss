$node-height: 48px !default;
// Minimum height for tree nodes in highest density is difficult to determine as
// developers can display arbitrary content. We use a minimum height which ensures
// that common content placed in tree nodes does not exceed (e.g. icons, checkboxes).
$node-minimum-height: 24px !default;
$node-maximum-height: $node-height !default;

$density-config: (
  height: (
    default: $node-height,
    maximum: $node-maximum-height,
    minimum: $node-minimum-height,
  )
) !default;
