@use 'sass:color';
@use 'sass:map';
@use 'sass:meta';
@use '../../token-utils';
@use '../../../theming/theming';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, sidenav);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // Currently zero, but it appears to be relevant for M3.
    // See: https://m3.material.io/components/navigation-drawer/overview
    container-shape: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  $is-dark: map.get($config, is-dark);
  $foreground: map.get($config, foreground);
  $background: map.get($config, background);
  $scrim-opacity: 0.6;
  $scrim-color: theming.get-color-from-palette($background, card, $scrim-opacity);
  $fallback-scrim-color: if($is-dark, rgba(#fff, $scrim-opacity), rgba(#000, $scrim-opacity));

  @return (
    container-divider-color: theming.get-color-from-palette($foreground, divider),
    container-background-color: theming.get-color-from-palette($background, dialog),
    container-text-color: theming.get-color-from-palette($foreground, text),
    content-background-color: theming.get-color-from-palette($background, background),
    content-text-color: theming.get-color-from-palette($foreground, text),

    // We use invert() here to have the darken the background color expected to be used.
    // If the background is light, we use a dark backdrop. If the background is dark, we
    // use a light backdrop. If the value isn't a color, Sass will throw an error so we
    // fall back to something generic.
    scrim-color: if(meta.type-of($scrim-color) == color,
      color.invert($scrim-color), $fallback-scrim-color),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
