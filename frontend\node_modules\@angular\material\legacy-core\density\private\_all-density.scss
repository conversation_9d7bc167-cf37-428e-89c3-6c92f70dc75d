@use '../../../core/theming/theming';
@use '../../../legacy-form-field/form-field-theme';
@use '../../../expansion/expansion-theme';
@use '../../../stepper/stepper-theme';
@use '../../../toolbar/toolbar-theme';
@use '../../../tree/tree-theme';
@use '../../../button-toggle/button-toggle-theme';

// Add legacy theme imports here, e.g.:
// @use '../../../<legacy-component>/<legacy-component>-theme';

// Includes all of the density styles.
/// @deprecated Use `mat.all-component-densities` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin all-legacy-component-densities($config-or-theme) {
  // In case a theme object has been passed instead of a configuration for
  // the density system, extract the density config from the theme object.
  $config: if(theming.private-is-theme-object($config-or-theme),
      theming.get-density-config($config-or-theme), $config-or-theme);

  @if $config == null {
    @error 'No density configuration specified.';
  }

  // TODO: COMP-309: Do not use individual mixins. Instead, use the all-theme mixin and only
  // specify a `density` config while setting `color` and `typography` to `null`. This is currently
  // not possible as it would introduce a circular dependency for density because the `mat-core`
  // mixin that is transitively loaded by the `all-theme` file, imports `all-density` which
  // would then load `all-theme` again. This ultimately results a circular dependency.

  // Add legacy density includes here, e.g.:
  // @include <legacy-component>-theme.density($config);
  @include form-field-theme.density($config);
  @include expansion-theme.density($config);
  @include stepper-theme.density($config);
  @include toolbar-theme.density($config);
  @include tree-theme.density($config);
  @include button-toggle-theme.density($config);
}


/// @deprecated Use `mat.all-component-densities` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin angular-material-legacy-density($config-or-theme) {
  @include all-legacy-component-densities($config-or-theme);
}
