@use './typography';
@use '../../autocomplete/autocomplete-theme';
@use '../../badge/badge-theme';
@use '../../bottom-sheet/bottom-sheet-theme';
@use '../../button/button-theme';
@use '../../button/icon-button-theme';
@use '../../button/fab-theme';
@use '../../button-toggle/button-toggle-theme';
@use '../../card/card-theme';
@use '../../checkbox/checkbox-theme';
@use '../../chips/chips-theme';
@use '../../divider/divider-theme';
@use '../../table/table-theme';
@use '../../datepicker/datepicker-theme';
@use '../../dialog/dialog-theme';
@use '../../expansion/expansion-theme';
@use '../../grid-list/grid-list-theme';
@use '../../icon/icon-theme';
@use '../../input/input-theme';
@use '../../list/list-theme';
@use '../../menu/menu-theme';
@use '../../paginator/paginator-theme';
@use '../../progress-bar/progress-bar-theme';
@use '../../progress-spinner/progress-spinner-theme';
@use '../../radio/radio-theme';
@use '../../select/select-theme';
@use '../../sidenav/sidenav-theme';
@use '../../slide-toggle/slide-toggle-theme';
@use '../../slider/slider-theme';
@use '../../stepper/stepper-theme';
@use '../../sort/sort-theme';
@use '../../tabs/tabs-theme';
@use '../../toolbar/toolbar-theme';
@use '../../tooltip/tooltip-theme';
@use '../../snack-bar/snack-bar-theme';
@use '../../form-field/form-field-theme';
@use '../../tree/tree-theme';
@use '../theming/theming';
@use '../core-theme';

// Includes all of the typographic styles.
@mixin all-component-typographies($config-or-theme: null) {
  $config: if(theming.private-is-theme-object($config-or-theme),
      theming.get-typography-config($config-or-theme), $config-or-theme);

  // If no actual color configuration has been specified, create a default one.
  @if not $config {
    $config: typography.define-typography-config();
  }

  // TODO: COMP-309: Do not use individual mixins. Instead, use the all-theme mixin and only
  // specify a `typography` config while setting `color` and `density` to `null`. This is currently
  // not possible as it would introduce a circular dependency for typography because the `mat-core`
  // mixin that is transitively loaded by the `all-theme` file, imports `all-typography` which
  // would then load `all-theme` again. This ultimately results a circular dependency.
  @include badge-theme.typography($config);
  @include typography.typography-hierarchy($config);
  @include bottom-sheet-theme.typography($config);
  @include button-toggle-theme.typography($config);
  @include divider-theme.typography($config);
  @include datepicker-theme.typography($config);
  @include expansion-theme.typography($config);
  @include grid-list-theme.typography($config);
  @include icon-theme.typography($config);
  @include progress-spinner-theme.typography($config);
  @include sidenav-theme.typography($config);
  @include stepper-theme.typography($config);
  @include sort-theme.typography($config);
  @include toolbar-theme.typography($config);
  @include tree-theme.typography($config);
  @include core-theme.typography($config);
  @include card-theme.typography($config);
  @include progress-bar-theme.typography($config);
  @include tooltip-theme.typography($config);
  @include form-field-theme.typography($config);
  @include input-theme.typography($config);
  @include select-theme.typography($config);
  @include autocomplete-theme.typography($config);
  @include dialog-theme.typography($config);
  @include chips-theme.typography($config);
  @include slide-toggle-theme.typography($config);
  @include radio-theme.typography($config);
  @include slider-theme.typography($config);
  @include menu-theme.typography($config);
  @include list-theme.typography($config);
  @include paginator-theme.typography($config);
  @include tabs-theme.typography($config);
  @include checkbox-theme.typography($config);
  @include button-theme.typography($config);
  @include icon-button-theme.typography($config);
  @include fab-theme.typography($config);
  @include snack-bar-theme.typography($config);
  @include table-theme.typography($config);
}

// @deprecated Use `all-component-typographies`.
@mixin angular-material-typography($config-or-theme: null) {
  @include all-component-typographies($config-or-theme);
}
