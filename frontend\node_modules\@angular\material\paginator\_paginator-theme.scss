@use 'sass:meta';
@use '../core/tokens/m2/mat/paginator' as tokens-mat-paginator;
@use '../core/style/sass-utils';
@use '../core/typography/typography';
@use '../core/theming/theming';
@use '../core/tokens/token-utils';
@use '../form-field/form-field-density';

@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-paginator.$prefix,
      tokens-mat-paginator.get-color-tokens($config));
  }
}

@mixin typography($config-or-theme) {
  $config: typography.private-typography-to-2018-config(
      theming.get-typography-config($config-or-theme));

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-paginator.$prefix,
      tokens-mat-paginator.get-typography-tokens($config));
  }
}

@mixin density($config-or-theme) {
  $density-scale: theming.get-density-config($config-or-theme);

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-paginator.$prefix,
      tokens-mat-paginator.get-density-tokens($density-scale));
  }

  // TODO: this should be done through tokens once the form field has been switched over.
  .mat-mdc-paginator {
    // We need the form field to be narrower in order to fit into the paginator,
    // so we set its density to be -4 or denser.
    @if ((meta.type-of($density-scale) == 'number' and $density-scale >= -4) or
         $density-scale == maximum) {
      @include form-field-density.private-form-field-density(-4);
    }
    @else {
      @include form-field-density.private-form-field-density($density-scale);
    }
  }
}

@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-paginator') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
