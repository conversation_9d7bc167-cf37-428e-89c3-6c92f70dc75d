@use 'sass:map';
@use 'sass:meta';
@use '../../token-utils';
@use '../../../typography/typography-utils';
@use '../../../theming/theming';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, standard-button-toggle);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    shape: 4px,
    hover-state-layer-opacity: 0.04,
    focus-state-layer-opacity: 0.12,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  $foreground: map.get($config, foreground);
  $background: map.get($config, background);
  $theme-divider-color: theming.get-color-from-palette($foreground, divider);

  // By default the theme usually has an rgba color for the dividers, which can
  // stack up with the background of a button toggle. This can cause the border
  // of a selected toggle to look different from an deselected one. We use a solid
  // color to ensure that the border always stays the same.
  $divider-color: if(meta.type-of($theme-divider-color) == color,
    theming.private-rgba-to-hex($theme-divider-color, map.get($background, card)),
    $theme-divider-color
  );

  @return (
    text-color: theming.get-color-from-palette($foreground, text),
    background-color: theming.get-color-from-palette($background, card),
    state-layer-color: theming.get-color-from-palette($background, focused-button, 1),
    selected-state-background-color: theming.get-color-from-palette($background, selected-button),
    selected-state-text-color: theming.get-color-from-palette($foreground, text),
    disabled-state-text-color: theming.get-color-from-palette($foreground, disabled-button),
    disabled-state-background-color: theming.get-color-from-palette($background, card),
    disabled-selected-state-text-color: theming.get-color-from-palette($foreground, text),
    disabled-selected-state-background-color:
      theming.get-color-from-palette($background, selected-disabled-button),
    divider-color: $divider-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  @return (
    // TODO(crisbeto): other components have tokens for all typography dimensions (font weight,
    // letter spacing etc). The button toggle only has the font to match what it had in the
    // old theming API and to reduce internal breakages. We should introduce more typography
    // tokens at some point.
    text-font: typography-utils.font-family($config),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  $density-scale: theming.clamp-density($config, -4);
  $size-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
    -4: 24px,
  );

  @return (
    height: map.get($size-scale, $density-scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
