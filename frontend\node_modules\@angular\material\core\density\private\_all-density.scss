@use '../../theming/theming';
@use '../../../button/button-theme';
@use '../../../button/icon-button-theme';
@use '../../../button/fab-theme';
@use '../../../expansion/expansion-theme';
@use '../../../stepper/stepper-theme';
@use '../../../toolbar/toolbar-theme';
@use '../../../tree/tree-theme';
@use '../../../paginator/paginator-theme';
@use '../../../form-field/form-field-theme';
@use '../../../button-toggle/button-toggle-theme';
@use '../../../card/card-theme';
@use '../../../progress-bar/progress-bar-theme';
@use '../../../progress-spinner/progress-spinner-theme';
@use '../../../tooltip/tooltip-theme';
@use '../../../input/input-theme';
@use '../../../autocomplete/autocomplete-theme';
@use '../../../checkbox/checkbox-theme';
@use '../../../core/core-theme';
@use '../../../select/select-theme';
@use '../../../dialog/dialog-theme';
@use '../../../chips/chips-theme';
@use '../../../slide-toggle/slide-toggle-theme';
@use '../../../radio/radio-theme';
@use '../../../slider/slider-theme';
@use '../../../menu/menu-theme';
@use '../../../list/list-theme';
@use '../../../tabs/tabs-theme';
@use '../../../snack-bar/snack-bar-theme';
@use '../../../table/table-theme';

// Includes all of the density styles.
@mixin all-component-densities($config-or-theme) {
  // In case a theme object has been passed instead of a configuration for
  // the density system, extract the density config from the theme object.
  $config: if(theming.private-is-theme-object($config-or-theme),
      theming.get-density-config($config-or-theme), $config-or-theme);

  @if $config == null {
    @error 'No density configuration specified.';
  }

  // TODO: COMP-309: Do not use individual mixins. Instead, use the all-theme mixin and only
  // specify a `density` config while setting `color` and `typography` to `null`. This is currently
  // not possible as it would introduce a circular dependency for density because the `mat-core`
  // mixin that is transitively loaded by the `all-theme` file, imports `all-density` which
  // would then load `all-theme` again. This ultimately results a circular dependency.
  @include form-field-theme.density($config);
  @include card-theme.density($config);
  @include progress-bar-theme.density($config);
  @include progress-spinner-theme.density($config);
  @include tooltip-theme.density($config);
  @include input-theme.density($config);
  @include core-theme.density($config);
  @include select-theme.density($config);
  @include checkbox-theme.density($config);
  @include autocomplete-theme.density($config);
  @include dialog-theme.density($config);
  @include chips-theme.density($config);
  @include slide-toggle-theme.density($config);
  @include radio-theme.density($config);
  @include slider-theme.density($config);
  @include menu-theme.density($config);
  @include list-theme.density($config);
  @include paginator-theme.density($config);
  @include tabs-theme.density($config);
  @include snack-bar-theme.density($config);
  @include button-theme.density($config);
  @include icon-button-theme.density($config);
  @include fab-theme.density($config);
  @include table-theme.density($config);
  @include expansion-theme.density($config);
  @include stepper-theme.density($config);
  @include toolbar-theme.density($config);
  @include tree-theme.density($config);
  @include button-toggle-theme.density($config);
}


// @deprecated Use `all-component-densities`.
@mixin angular-material-density($config-or-theme) {
  @include all-component-densities($config-or-theme);
}
