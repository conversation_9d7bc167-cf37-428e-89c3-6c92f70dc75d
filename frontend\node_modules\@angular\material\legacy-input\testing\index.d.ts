import { InputHarnessFilters as LegacyInputHarnessFilters } from '@angular/material/input/testing';
import { NativeOptionHarnessFilters as LegacyNativeOptionHarnessFilters } from '@angular/material/input/testing';
import { NativeSelectHarnessFilters as LegacyNativeSelectHarnessFilters } from '@angular/material/input/testing';
import { MatInputHarness as MatLegacyInputHarness } from '@angular/material/input/testing';
import { MatNativeOptionHarness as MatLegacyNativeOptionHarness } from '@angular/material/input/testing';
import { MatNativeSelectHarness as MatLegacyNativeSelectHarness } from '@angular/material/input/testing';

export { LegacyInputHarnessFilters }

export { LegacyNativeOptionHarnessFilters }

export { LegacyNativeSelectHarnessFilters }

export { MatLegacyInputHarness }

export { MatLegacyNativeOptionHarness }

export { MatLegacyNativeSelectHarness }

export { }
