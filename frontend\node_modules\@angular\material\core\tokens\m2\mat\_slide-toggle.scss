@use '../../token-utils';
@use '../../../style/sass-utils';
@use '../../../typography/typography-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, slide-toggle);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  @return ();
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  // TODO(amysorto): The earlier implementation of the slide-toggle used MDC's APIs to create the
  // typography tokens. As a result, we unintentionally allowed `null` typography configs to be
  // passed in. Since there a lot of apps that now depend on this pattern, we need this temporary
  // fallback.
  @if ($config == null) {
    $config: mdc-helpers.private-fallback-typography-from-mdc();
  }

  @return (
    label-text-font: typography-utils.font-family($config),
    label-text-size: typography-utils.font-size($config, body-2),
    label-text-tracking: typography-utils.letter-spacing($config, body-2),
    label-text-line-height: typography-utils.line-height($config, body-2),
    label-text-weight: typography-utils.font-weight($config, body-2),
  );
 }

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
          get-unthemable-tokens(),
          get-color-tokens(token-utils.$placeholder-color-config),
          get-typography-tokens(token-utils.$placeholder-typography-config),
          get-density-tokens(token-utils.$placeholder-density-config)
  );
}
