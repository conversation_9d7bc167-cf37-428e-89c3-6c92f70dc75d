@use '../../core/typography/typography';
@use '../../core/theming/theming';
@use '../option/option-theme';
@use '../option/optgroup-theme';
@use '../../legacy-button/button-theme';
@use '../../legacy-card/card-theme';
@use '../../legacy-checkbox/checkbox-theme';
@use '../../legacy-progress-bar/progress-bar-theme';
@use '../../legacy-progress-spinner/progress-spinner-theme';
@use '../../legacy-tooltip/tooltip-theme';
@use '../../legacy-input/input-theme';
@use '../../legacy-form-field/form-field-theme';
@use '../../legacy-select/select-theme';
@use '../../legacy-autocomplete/autocomplete-theme';
@use '../../legacy-dialog/dialog-theme';
@use '../../legacy-chips/chips-theme';
@use '../../legacy-slide-toggle/slide-toggle-theme';
@use '../../legacy-radio/radio-theme';
@use '../../legacy-slider/slider-theme';
@use '../../legacy-menu/menu-theme';
@use '../../legacy-list/list-theme';
@use '../../legacy-paginator/paginator-theme';
@use '../../legacy-tabs/tabs-theme';
@use '../../legacy-snack-bar/snack-bar-theme';
@use '../../legacy-table/table-theme';
@use '../../badge/badge-theme';
@use '../../bottom-sheet/bottom-sheet-theme';
@use '../../button-toggle/button-toggle-theme';
@use '../../divider/divider-theme';
@use '../../datepicker/datepicker-theme';
@use '../../expansion/expansion-theme';
@use '../../grid-list/grid-list-theme';
@use '../../icon/icon-theme';
@use '../../sidenav/sidenav-theme';
@use '../../stepper/stepper-theme';
@use '../../sort/sort-theme';
@use '../../toolbar/toolbar-theme';
@use '../../tree/tree-theme';

// Includes all of the typographic styles.
/// @deprecated Use `mat.all-component-typographies` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin all-legacy-component-typographies($config-or-theme: null) {
  $config: if(theming.private-is-theme-object($config-or-theme),
      theming.get-typography-config($config-or-theme), $config-or-theme);

  // If no actual color configuration has been specified, create a default one.
  @if not $config {
    $config: typography.define-legacy-typography-config();
  }

  // TODO: COMP-309: Do not use individual mixins. Instead, use the all-theme mixin and only
  // specify a `typography` config while setting `color` and `density` to `null`. This is currently
  // not possible as it would introduce a circular dependency for typography because the `mat-core`
  // mixin that is transitively loaded by the `all-theme` file, imports `all-typography` which
  // would then load `all-theme` again. This ultimately results a circular dependency.


  // Components without MDC versions.
  @include typography.legacy-typography-hierarchy($config);
  @include badge-theme.typography($config);
  @include bottom-sheet-theme.typography($config);
  @include button-toggle-theme.typography($config);
  @include divider-theme.typography($config);
  @include datepicker-theme.typography($config);
  @include expansion-theme.typography($config);
  @include grid-list-theme.typography($config);
  @include icon-theme.typography($config);
  @include sidenav-theme.typography($config);
  @include stepper-theme.typography($config);
  @include sort-theme.typography($config);
  @include toolbar-theme.typography($config);
  @include tree-theme.typography($config);

  // Legacy components.
  @include option-theme.typography($config);
  @include optgroup-theme.typography($config);
  @include button-theme.typography($config);
  @include card-theme.typography($config);
  @include progress-bar-theme.typography($config);
  @include progress-spinner-theme.typography($config);
  @include tooltip-theme.typography($config);
  @include input-theme.typography($config);
  @include form-field-theme.typography($config);
  @include select-theme.typography($config);
  @include checkbox-theme.typography($config);
  @include autocomplete-theme.typography($config);
  @include dialog-theme.typography($config);
  @include chips-theme.typography($config);
  @include slide-toggle-theme.typography($config);
  @include tabs-theme.typography($config);
  @include radio-theme.typography($config);
  @include slider-theme.typography($config);
  @include menu-theme.typography($config);
  @include list-theme.typography($config);
  @include paginator-theme.typography($config);
  @include snack-bar-theme.typography($config);
  @include table-theme.typography($config);
}

/// @deprecated Use `mat.all-component-typographies` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin angular-material-legacy-typography($config-or-theme: null) {
  @include all-legacy-component-typographies($config-or-theme);
}
