/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @module
 * @description
 * Entry point for all public APIs of the common/testing package.
 */
export { SpyLocation } from './location_mock';
export { MockLocationStrategy } from './mock_location_strategy';
export { MOCK_PLATFORM_LOCATION_CONFIG, MockPlatformLocation } from './mock_platform_location';
export { provideLocationMocks } from './provide_location_mocks';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVzdGluZy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvbW1vbi90ZXN0aW5nL3NyYy90ZXN0aW5nLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVIOzs7O0dBSUc7QUFDSCxPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDNUMsT0FBTyxFQUFDLG9CQUFvQixFQUFDLE1BQU0sMEJBQTBCLENBQUM7QUFDOUQsT0FBTyxFQUFDLDZCQUE2QixFQUFFLG9CQUFvQixFQUE2QixNQUFNLDBCQUEwQixDQUFDO0FBQ3pILE9BQU8sRUFBQyxvQkFBb0IsRUFBQyxNQUFNLDBCQUEwQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8qKlxuICogQG1vZHVsZVxuICogQGRlc2NyaXB0aW9uXG4gKiBFbnRyeSBwb2ludCBmb3IgYWxsIHB1YmxpYyBBUElzIG9mIHRoZSBjb21tb24vdGVzdGluZyBwYWNrYWdlLlxuICovXG5leHBvcnQge1NweUxvY2F0aW9ufSBmcm9tICcuL2xvY2F0aW9uX21vY2snO1xuZXhwb3J0IHtNb2NrTG9jYXRpb25TdHJhdGVneX0gZnJvbSAnLi9tb2NrX2xvY2F0aW9uX3N0cmF0ZWd5JztcbmV4cG9ydCB7TU9DS19QTEFURk9STV9MT0NBVElPTl9DT05GSUcsIE1vY2tQbGF0Zm9ybUxvY2F0aW9uLCBNb2NrUGxhdGZvcm1Mb2NhdGlvbkNvbmZpZ30gZnJvbSAnLi9tb2NrX3BsYXRmb3JtX2xvY2F0aW9uJztcbmV4cG9ydCB7cHJvdmlkZUxvY2F0aW9uTW9ja3N9IGZyb20gJy4vcHJvdmlkZV9sb2NhdGlvbl9tb2Nrcyc7XG4iXX0=