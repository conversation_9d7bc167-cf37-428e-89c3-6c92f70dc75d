@use 'sass:color';
@use 'sass:map';
@use 'sass:meta';
@use '../core/theming/theming';

@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);
  $background: map.get($config, background);
  $foreground: map.get($config, foreground);

  .mat-sort-header-arrow {
    $table-background: theming.get-color-from-palette($background, 'card');
    $text-color: theming.get-color-from-palette($foreground, secondary-text);

    // Because the arrow is made up of multiple elements that are stacked on top of each other,
    // we can't use the semi-transparent color from the theme directly. If the value is a color
    // *type*, we convert it into a solid color by taking the opacity from the rgba value and
    // using the value to determine the percentage of the background to put into foreground
    // when mixing the colors together. Otherwise, if it resolves to something different
    // (e.g. it resolves to a CSS variable), we use the color directly.
    @if (meta.type-of($table-background) == color and meta.type-of($text-color) == color) {
      $text-opacity: opacity($text-color);
      color: color.mix($table-background, rgba($text-color, 1), (1 - $text-opacity) * 100%);
    }
    @else {
      color: $text-color;
    }
  }
}

@mixin typography($config-or-theme) {}

@mixin density($config-or-theme) {}

@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-sort') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
