{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, message_r8.isUser, !message_r8.isUser));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r8.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 3, message_r8.timestamp, \"short\"));\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 19)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11, 12);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 13);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 7, 9, \"div\", 14);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"textarea\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(6, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.messages = [];\n    this.currentMessage = '';\n    this.isLoading = false;\n    this.isAuthenticated = false;\n    this.currentSessionId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.isLoadingHistory = false;\n    this.hasInitiallyLoaded = false;\n    // Scroll management\n    this.shouldScrollToBottom = true;\n    this.lastScrollHeight = 0;\n  }\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: token => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory();\n      },\n      error: error => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  // Listen for scroll events to load more history\n  onScroll(event) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    // If user scrolled to within 100px of the top, load more history\n    if (scrollTop < 100 && this.hasNextPage && !this.isLoadingHistory) {\n      this.lastScrollHeight = scrollHeight;\n      this.loadMoreHistory();\n    }\n  }\n  loadChatHistory(page = 1, append = false) {\n    if (!this.isAuthenticated) return;\n    this.isLoadingHistory = true;\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        if (append) {\n          // For pagination - prepend older messages to beginning\n          this.messages = [...newMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - replace all messages (already in chronological order)\n          this.messages = newMessages;\n          this.shouldScrollToBottom = true;\n        }\n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        this.isLoadingHistory = false;\n      },\n      error: error => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // Continue with existing messages\n      }\n    });\n  }\n  // Convert Django ChatMessage to frontend Message format\n  convertChatMessageToMessage(chatMessage) {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined\n    };\n  }\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n  // Maintain scroll position when loading older messages\n  maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.isLoading = true;\n    // Set flag to scroll to bottom after new messages\n    this.shouldScrollToBottom = true;\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        // Convert and add user message\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        // Add both messages to the end of chat (chronological order)\n        this.messages.push(userMessage);\n        this.messages.push(botMessage);\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        const errorMessage = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n  clearHistory() {\n    this.messages = [];\n  }\n  refreshHistory() {\n    this.currentPage = 1;\n    this.loadChatHistory();\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  adjustTextareaHeight(event) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\"], [1, \"message-time\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"auth-loading\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-anonymous-user\");\n        i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 10, 7, \"div\", 0);\n        i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i2.DatePipe],\n    styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  max-width: 800px;\\n  margin: 0 auto;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  position: relative;\\n  overflow: hidden;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\\n  box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 24px 40px 140px 40px;\\n  background: transparent;\\n  max-height: calc(100vh - 200px);\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 10; \\n\\n  \\n\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(241, 241, 241, 0.5);\\n  border-radius: 10px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(135deg, #c1c1c1 0%, #a8a8a8 100%);\\n  border-radius: 10px;\\n  -webkit-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(135deg, #a8a8a8 0%, #9ca3af 100%);\\n}\\n\\n\\n\\n.pagination-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 16px 0;\\n  margin-bottom: 20px;\\n}\\n.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 3px solid rgba(55, 65, 81, 0.1);\\n  border-top: 3px solid #374151;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  display: flex;\\n  animation: _ngcontent-%COMP%_slideInUp 0.3s ease-out;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: #e5e5ea;\\n  color: #000;\\n  max-width: 75%;\\n  border-radius: 18px 18px 4px 18px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transform: translateY(0);\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n.message.bot-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  color: #000;\\n  max-width: 75%;\\n  border-radius: 18px 18px 18px 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transform: translateY(0);\\n  transition: all 0.2s ease;\\n  position: relative;\\n  border: 1px solid rgba(224, 224, 224, 0.5);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  word-wrap: break-word;\\n  position: relative;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  line-height: 1.5;\\n  white-space: pre-wrap;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e93;\\n  margin-top: 8px;\\n  text-align: right;\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  justify-content: flex-end;\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n  justify-content: flex-start;\\n}\\n\\n.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  padding: 24px 40px 32px 40px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 100%;\\n  max-width: 800px;\\n  z-index: 50; \\n\\n  border-top: 1px solid rgba(224, 224, 224, 0.5);\\n  transition: all 0.3s ease;\\n}\\n.input-container.centered[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100%;\\n  max-width: 800px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  z-index: 50;\\n  padding: 32px 40px;\\n  bottom: auto;\\n  border-radius: 20px;\\n  border: 1px solid rgba(224, 224, 224, 0.5);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n}\\n.input-container.bottom[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  border-top: 1px solid rgba(224, 224, 224, 0.5);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  width: 100%;\\n  max-width: 800px;\\n  z-index: 50;\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 12px;\\n  border: 2px solid rgba(209, 213, 219, 0.8);\\n  border-radius: 28px;\\n  padding: 6px 8px 6px 20px;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  position: relative;\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: rgba(156, 163, 175, 0.8);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n  transform: translateY(-2px);\\n}\\n.input-wrapper[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, transparent 0%, rgba(156, 163, 175, 0.1) 50%, transparent 100%);\\n  border-radius: 30px;\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within::before {\\n  opacity: 1;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  resize: none;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  padding: 14px 0;\\n  min-height: 24px;\\n  max-height: 120px;\\n  font-family: inherit;\\n  overflow-y: auto;\\n  transition: all 0.3s ease;\\n  color: #374151;\\n  \\n\\n}\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-weight: 400;\\n}\\n.message-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);\\n  border-radius: 4px;\\n  -webkit-transition: background 0.3s ease;\\n  transition: background 0.3s ease;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #fff;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  margin: 2px;\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  box-shadow: 0 4px 12px rgba(55, 65, 81, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 0;\\n  height: 0;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%);\\n  transition: all 0.3s ease;\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 8px 20px rgba(55, 65, 81, 0.4);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  width: 100%;\\n  height: 100%;\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0) scale(1);\\n  box-shadow: 0 2px 8px rgba(55, 65, 81, 0.3);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  transition: transform 0.3s ease;\\n  z-index: 1;\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1) rotate(5deg);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 4px 0;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n.auth-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  color: #666;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #374151;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 1rem;\\n}\\n.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #6b7280;\\n  margin: 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    opacity: 0.4;\\n    transform: scale(0.8) translateY(0);\\n  }\\n  30% {\\n    opacity: 1;\\n    transform: scale(1.2) translateY(-4px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px) scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chat-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    height: 100vh;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: 20px 20px 140px 20px;\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: 20px 20px 24px 20px;\\n    max-width: 100%;\\n  }\\n  .input-container.centered[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: 20px;\\n    margin: 0 16px;\\n    width: calc(100% - 32px);\\n  }\\n  .message[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 85%;\\n  }\\n  .input-wrapper[_ngcontent-%COMP%] {\\n    padding: 4px 6px 4px 16px;\\n    gap: 8px;\\n  }\\n  .send-button[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;\\n}\\n\\n\\n\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #374151;\\n  outline-offset: 2px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "message_r8", "isUser", "ɵɵadvance", "ɵɵtextInterpolate", "text", "ɵɵpipeBind2", "timestamp", "ɵɵlistener", "ChatComponent_div_1_div_1_Template_div_scroll_0_listener", "$event", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "onScroll", "ɵɵtemplate", "ChatComponent_div_1_div_1_div_2_Template", "ChatComponent_div_1_div_1_div_3_Template", "ChatComponent_div_1_div_1_div_4_Template", "ctx_r2", "isLoadingHistory", "messages", "isLoading", "ChatComponent_div_1_div_1_Template", "ChatComponent_div_1_Template_textarea_ngModelChange_4_listener", "_r12", "ctx_r11", "currentMessage", "ChatComponent_div_1_Template_textarea_keydown_4_listener", "ctx_r13", "onKeyPress", "ChatComponent_div_1_Template_textarea_input_4_listener", "ctx_r14", "adjustTextareaHeight", "ChatComponent_div_1_Template_button_click_7_listener", "ctx_r15", "sendMessage", "ɵɵnamespaceSVG", "ctx_r0", "length", "ɵɵclassProp", "trim", "ChatComponent", "constructor", "authService", "isAuthenticated", "currentSessionId", "currentPage", "hasNextPage", "hasInitiallyLoaded", "shouldScrollToBottom", "lastScrollHeight", "ngOnInit", "ensureAuthenticated", "subscribe", "next", "token", "console", "log", "loadChatHistory", "error", "ngAfterViewChecked", "scrollToBottom", "event", "element", "target", "scrollTop", "scrollHeight", "loadMoreHistory", "page", "append", "undefined", "response", "newMessages", "results", "map", "msg", "convertChatMessageToMessage", "maintainScrollPosition", "chatMessage", "id", "message", "sender", "Date", "session", "prompt", "model", "setTimeout", "messagesContainer", "nativeElement", "newScrollHeight", "scrollDifference", "messageToSend", "sendMessageToChatbot", "userMessage", "user_message", "botMessage", "bot_message", "push", "errorMessage", "clearHistory", "refreshHistory", "key", "shift<PERSON>ey", "preventDefault", "textarea", "style", "height", "Math", "min", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_div_1_Template", "ChatComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\3a9arAI-Agentic-Workflow\\frontend\\src\\app\\chat\\chat.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\3a9arAI-Agentic-Workflow\\frontend\\src\\app\\chat\\chat.component.html"], "sourcesContent": ["import { Component, OnInit, ElementRef, ViewChild, AfterViewChecked } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\n\ninterface Message {\n  id?: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  session?: number;\n  prompt?: number;\n  model?: number;\n}\n\n// Django ChatMessage structure\ninterface ChatMessage {\n  id: number;\n  session: number;\n  sender: 'user' | 'bot';\n  message: string;\n  timestamp: string;\n  prompt: number | null;\n  model: number | null;\n}\n\n// Django paginated response\ninterface ChatHistoryResponse {\n  count: number;\n  next: string | null;\n  previous: string | null;\n  results: ChatMessage[];\n}\n\n// Django chatbot response\ninterface ChatbotResponse {\n  user_message: ChatMessage;\n  bot_message: ChatMessage;\n}\n\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.scss']\n})\nexport class ChatComponent implements OnInit, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  currentMessage: string = '';\n  isLoading: boolean = false;\n  isAuthenticated: boolean = false;\n  currentSessionId: number | null = null;\n  \n  // Pagination\n  currentPage: number = 1;\n  hasNextPage: boolean = false;\n  isLoadingHistory: boolean = false;\n  private hasInitiallyLoaded: boolean = false;\n  \n  // Scroll management\n  private shouldScrollToBottom: boolean = true;\n  private lastScrollHeight: number = 0;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: (token) => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory();\n      },\n      error: (error) => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  // Listen for scroll events to load more history\n  onScroll(event: any) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    \n    // If user scrolled to within 100px of the top, load more history\n    if (scrollTop < 100 && this.hasNextPage && !this.isLoadingHistory) {\n      this.lastScrollHeight = scrollHeight;\n      this.loadMoreHistory();\n    }\n  }\n\n  loadChatHistory(page: number = 1, append: boolean = false) {\n    if (!this.isAuthenticated) return;\n\n    this.isLoadingHistory = true;\n\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatHistoryResponse) => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        \n        if (append) {\n          // For pagination - prepend older messages to beginning\n          this.messages = [...newMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - replace all messages (already in chronological order)\n          this.messages = newMessages;\n          this.shouldScrollToBottom = true;\n        }\n        \n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        \n        this.isLoadingHistory = false;\n      },\n      error: (error) => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // Continue with existing messages\n      }\n    });\n  }\n\n  // Convert Django ChatMessage to frontend Message format\n  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined\n    };\n  }\n\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n\n  // Maintain scroll position when loading older messages\n  private maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.isLoading = true;\n    \n    // Set flag to scroll to bottom after new messages\n    this.shouldScrollToBottom = true;\n\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatbotResponse) => {\n        // Convert and add user message\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        \n        // Add both messages to the end of chat (chronological order)\n        this.messages.push(userMessage);\n        this.messages.push(botMessage);\n        \n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        \n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        const errorMessage: Message = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n\n  clearHistory() {\n    this.messages = [];\n  }\n\n  refreshHistory() {\n    this.currentPage = 1;\n    this.loadChatHistory();\n  }\n\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  adjustTextareaHeight(event: any) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n}", "<!-- chat.component.html -->\n\n<!-- Anonymous user component runs in background -->\n<app-anonymous-user></app-anonymous-user>\n\n<div class=\"chat-container\" *ngIf=\"isAuthenticated\">\n  <!-- Messages container - only show when there are messages -->\n  <div \n    #messagesContainer\n    class=\"messages-container\" \n    (scroll)=\"onScroll($event)\"\n    *ngIf=\"messages.length > 0\">\n    \n    <!-- Loading indicator for pagination at the top -->\n    <div class=\"pagination-loading\" *ngIf=\"isLoadingHistory\">\n      <div class=\"loading-spinner\"></div>\n    </div>\n    \n    <div class=\"message\"\n         *ngFor=\"let message of messages\"\n         [ngClass]=\"{'user-message': message.isUser, 'bot-message': !message.isUser}\">\n      <div class=\"message-content\">\n        <div class=\"message-text\">{{ message.text }}</div>\n        <div class=\"message-time\">{{ message.timestamp | date:'short' }}</div>\n      </div>\n    </div>\n\n    <!-- Loading indicator for current message -->\n    <div class=\"message bot-message\" *ngIf=\"isLoading\">\n      <div class=\"message-content\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input container - always visible -->\n  <div class=\"input-container\" [class.centered]=\"messages.length === 0\" [class.bottom]=\"messages.length > 0\">\n    <div class=\"input-wrapper\">\n      <textarea\n        #messageTextarea\n        [(ngModel)]=\"currentMessage\"\n        (keydown)=\"onKeyPress($event)\"\n        (input)=\"adjustTextareaHeight($event)\"\n        placeholder=\"Ask anything\"\n        class=\"message-input\"\n        rows=\"1\">\n      </textarea>\n      <button\n        (click)=\"sendMessage()\"\n        class=\"send-button\"\n        [disabled]=\"!currentMessage.trim() || isLoading\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"currentColor\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Show loading message while authenticating -->\n<div *ngIf=\"!isAuthenticated\" class=\"auth-loading\">\n  <div class=\"loading-spinner\"></div>\n  <p>Initializing chat...</p>\n</div>"], "mappings": ";;;;;;;;ICcIA,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IAENH,EAAA,CAAAC,cAAA,cAEkF;IAEpDD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAI,MAAA,GAAsC;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAHrEH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,MAAA,GAAAD,UAAA,CAAAC,MAAA,EAA4E;IAEnDT,EAAA,CAAAU,SAAA,GAAkB;IAAlBV,EAAA,CAAAW,iBAAA,CAAAH,UAAA,CAAAI,IAAA,CAAkB;IAClBZ,EAAA,CAAAU,SAAA,GAAsC;IAAtCV,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAa,WAAA,OAAAL,UAAA,CAAAM,SAAA,WAAsC;;;;;IAKpEd,EAAA,CAAAC,cAAA,cAAmD;IAG7CD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;IA3BZH,EAAA,CAAAC,cAAA,kBAI8B;IAD5BD,EAAA,CAAAe,UAAA,oBAAAC,yDAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAUrB,EAAA,CAAAsB,WAAA,CAAAF,MAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;IAI3BjB,EAAA,CAAAwB,UAAA,IAAAC,wCAAA,kBAEM;IAENzB,EAAA,CAAAwB,UAAA,IAAAE,wCAAA,kBAOM;IAGN1B,EAAA,CAAAwB,UAAA,IAAAG,wCAAA,kBAQM;IACR3B,EAAA,CAAAG,YAAA,EAAM;;;;IAvB6BH,EAAA,CAAAU,SAAA,GAAsB;IAAtBV,EAAA,CAAAK,UAAA,SAAAuB,MAAA,CAAAC,gBAAA,CAAsB;IAK9B7B,EAAA,CAAAU,SAAA,GAAW;IAAXV,EAAA,CAAAK,UAAA,YAAAuB,MAAA,CAAAE,QAAA,CAAW;IASF9B,EAAA,CAAAU,SAAA,GAAe;IAAfV,EAAA,CAAAK,UAAA,SAAAuB,MAAA,CAAAG,SAAA,CAAe;;;;;;IAvBrD/B,EAAA,CAAAC,cAAA,aAAoD;IAElDD,EAAA,CAAAwB,UAAA,IAAAQ,kCAAA,iBA8BM;IAGNhC,EAAA,CAAAC,cAAA,aAA2G;IAIrGD,EAAA,CAAAe,UAAA,2BAAAkB,+DAAAhB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAgB,IAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAAa,OAAA,CAAAC,cAAA,GAAAnB,MAAA;IAAA,EAA4B,qBAAAoB,yDAAApB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAgB,IAAA;MAAA,MAAAI,OAAA,GAAAtC,EAAA,CAAAqB,aAAA;MAAA,OACjBrB,EAAA,CAAAsB,WAAA,CAAAgB,OAAA,CAAAC,UAAA,CAAAtB,MAAA,CAAkB;IAAA,EADD,mBAAAuB,uDAAAvB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAgB,IAAA;MAAA,MAAAO,OAAA,GAAAzC,EAAA,CAAAqB,aAAA;MAAA,OAEnBrB,EAAA,CAAAsB,WAAA,CAAAmB,OAAA,CAAAC,oBAAA,CAAAzB,MAAA,CAA4B;IAAA,EAFT;IAM9BjB,EAAA,CAAAI,MAAA;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAGmD;IAFjDD,EAAA,CAAAe,UAAA,mBAAA4B,qDAAA;MAAA3C,EAAA,CAAAkB,aAAA,CAAAgB,IAAA;MAAA,MAAAU,OAAA,GAAA5C,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAsB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvB7C,EAAA,CAAA8C,cAAA,EAA+F;IAA/F9C,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IA9CTH,EAAA,CAAAU,SAAA,GAAyB;IAAzBV,EAAA,CAAAK,UAAA,SAAA0C,MAAA,CAAAjB,QAAA,CAAAkB,MAAA,KAAyB;IA6BChD,EAAA,CAAAU,SAAA,GAAwC;IAAxCV,EAAA,CAAAiD,WAAA,aAAAF,MAAA,CAAAjB,QAAA,CAAAkB,MAAA,OAAwC,WAAAD,MAAA,CAAAjB,QAAA,CAAAkB,MAAA;IAI/DhD,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAK,UAAA,YAAA0C,MAAA,CAAAX,cAAA,CAA4B;IAU5BpC,EAAA,CAAAU,SAAA,GAAgD;IAAhDV,EAAA,CAAAK,UAAA,cAAA0C,MAAA,CAAAX,cAAA,CAAAc,IAAA,MAAAH,MAAA,CAAAhB,SAAA,CAAgD;;;;;IAUxD/B,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,2BAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;ADvB7B,OAAM,MAAOgD,aAAa;EAmBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAhB/B,KAAAvB,QAAQ,GAAc,EAAE;IACxB,KAAAM,cAAc,GAAW,EAAE;IAC3B,KAAAL,SAAS,GAAY,KAAK;IAC1B,KAAAuB,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA5B,gBAAgB,GAAY,KAAK;IACzB,KAAA6B,kBAAkB,GAAY,KAAK;IAE3C;IACQ,KAAAC,oBAAoB,GAAY,IAAI;IACpC,KAAAC,gBAAgB,GAAW,CAAC;EAEW;EAE/CC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,WAAW,CAACS,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAGC,KAAK,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACb,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACc,eAAe,EAAE;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACf,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAgB,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACX,oBAAoB,EAAE;MAC7B,IAAI,CAACY,cAAc,EAAE;MACrB,IAAI,CAACZ,oBAAoB,GAAG,KAAK;;EAErC;EAEA;EACApC,QAAQA,CAACiD,KAAU;IACjB,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;IAC5B,MAAMC,SAAS,GAAGF,OAAO,CAACE,SAAS;IACnC,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;IAEzC;IACA,IAAID,SAAS,GAAG,GAAG,IAAI,IAAI,CAAClB,WAAW,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,EAAE;MACjE,IAAI,CAAC+B,gBAAgB,GAAGgB,YAAY;MACpC,IAAI,CAACC,eAAe,EAAE;;EAE1B;EAEAT,eAAeA,CAACU,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACvD,IAAI,CAAC,IAAI,CAACzB,eAAe,EAAE;IAE3B,IAAI,CAACzB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACwB,WAAW,CAACe,eAAe,CAACU,IAAI,EAAE,IAAI,CAACvB,gBAAgB,IAAIyB,SAAS,CAAC,CAACjB,SAAS,CAAC;MACnFC,IAAI,EAAGiB,QAA6B,IAAI;QACtC,MAAMC,WAAW,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,2BAA2B,CAACD,GAAG,CAAC,CAAC;QAEtF,IAAIN,MAAM,EAAE;UACV;UACA,IAAI,CAACjD,QAAQ,GAAG,CAAC,GAAGoD,WAAW,EAAE,GAAG,IAAI,CAACpD,QAAQ,CAAC;UAClD,IAAI,CAACyD,sBAAsB,EAAE;SAC9B,MAAM;UACL;UACA,IAAI,CAACzD,QAAQ,GAAGoD,WAAW;UAC3B,IAAI,CAACvB,oBAAoB,GAAG,IAAI;;QAGlC;QACA,IAAI,CAACH,WAAW,GAAGsB,IAAI;QACvB,IAAI,CAACrB,WAAW,GAAG,CAAC,CAACwB,QAAQ,CAACjB,IAAI;QAElC,IAAI,CAACnC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACxC,gBAAgB,GAAG,KAAK;QAC7B;MACF;KACD,CAAC;EACJ;EAEA;EACQyD,2BAA2BA,CAACE,WAAwB;IAC1D,OAAO;MACLC,EAAE,EAAED,WAAW,CAACC,EAAE;MAClB7E,IAAI,EAAE4E,WAAW,CAACE,OAAO;MACzBjF,MAAM,EAAE+E,WAAW,CAACG,MAAM,KAAK,MAAM;MACrC7E,SAAS,EAAE,IAAI8E,IAAI,CAACJ,WAAW,CAAC1E,SAAS,CAAC;MAC1C+E,OAAO,EAAEL,WAAW,CAACK,OAAO;MAC5BC,MAAM,EAAEN,WAAW,CAACM,MAAM,IAAId,SAAS;MACvCe,KAAK,EAAEP,WAAW,CAACO,KAAK,IAAIf;KAC7B;EACH;EAEA;EACAH,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpB,WAAW,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,EAAE;MAC9C,IAAI,CAACuC,eAAe,CAAC,IAAI,CAACZ,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;;EAEpD;EAEA;EACQ+B,sBAAsBA,CAAA;IAC5BS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAMxB,OAAO,GAAG,IAAI,CAACwB,iBAAiB,CAACC,aAAa;QACpD,MAAMC,eAAe,GAAG1B,OAAO,CAACG,YAAY;QAC5C,MAAMwB,gBAAgB,GAAGD,eAAe,GAAG,IAAI,CAACvC,gBAAgB;QAChEa,OAAO,CAACE,SAAS,GAAGyB,gBAAgB;;IAExC,CAAC,EAAE,EAAE,CAAC;EACR;EAEAvD,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,CAACc,IAAI,EAAE,IAAI,IAAI,CAACnB,SAAS,IAAI,CAAC,IAAI,CAACuB,eAAe,EAAE;MAC1E;;IAGF;IACA,MAAM+C,aAAa,GAAG,IAAI,CAACjE,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAAC4B,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAACN,WAAW,CAACiD,oBAAoB,CAACD,aAAa,EAAE,IAAI,CAAC9C,gBAAgB,IAAIyB,SAAS,CAAC,CAACjB,SAAS,CAAC;MACjGC,IAAI,EAAGiB,QAAyB,IAAI;QAClC;QACA,MAAMsB,WAAW,GAAG,IAAI,CAACjB,2BAA2B,CAACL,QAAQ,CAACuB,YAAY,CAAC;QAC3E,MAAMC,UAAU,GAAG,IAAI,CAACnB,2BAA2B,CAACL,QAAQ,CAACyB,WAAW,CAAC;QAEzE;QACA,IAAI,CAAC5E,QAAQ,CAAC6E,IAAI,CAACJ,WAAW,CAAC;QAC/B,IAAI,CAACzE,QAAQ,CAAC6E,IAAI,CAACF,UAAU,CAAC;QAE9B;QACA,IAAI,CAAC,IAAI,CAAClD,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAG0B,QAAQ,CAACuB,YAAY,CAACX,OAAO;;QAGvD,IAAI,CAAC9D,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC4B,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMuC,YAAY,GAAY;UAC5BhG,IAAI,EAAE,sEAAsE;UAC5EH,MAAM,EAAE,KAAK;UACbK,SAAS,EAAE,IAAI8E,IAAI;SACpB;QACD,IAAI,CAAC9D,QAAQ,CAAC6E,IAAI,CAACC,YAAY,CAAC;QAChC,IAAI,CAAC7E,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC4B,oBAAoB,GAAG,IAAI;MAClC;KACD,CAAC;EACJ;EAEAkD,YAAYA,CAAA;IACV,IAAI,CAAC/E,QAAQ,GAAG,EAAE;EACpB;EAEAgF,cAAcA,CAAA;IACZ,IAAI,CAACtD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACY,eAAe,EAAE;EACxB;EAEA7B,UAAUA,CAACiC,KAAoB;IAC7B,IAAIA,KAAK,CAACuC,GAAG,KAAK,OAAO,IAAI,CAACvC,KAAK,CAACwC,QAAQ,EAAE;MAC5CxC,KAAK,CAACyC,cAAc,EAAE;MACtB,IAAI,CAACpE,WAAW,EAAE;;EAEtB;EAEAH,oBAAoBA,CAAC8B,KAAU;IAC7B,MAAM0C,QAAQ,GAAG1C,KAAK,CAACE,MAAM;IAC7BwC,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BF,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAACtC,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;EACrE;EAEQL,cAAcA,CAAA;IACpByB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAMxB,OAAO,GAAG,IAAI,CAACwB,iBAAiB,CAACC,aAAa;QACpDzB,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,EAAE,CAAC;EACR;EAAC,QAAA2C,CAAA,G;qBArMUpE,aAAa,EAAAnD,EAAA,CAAAwH,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAbxE,aAAa;IAAAyE,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCxC1B/H,EAAA,CAAAE,SAAA,yBAAyC;QAEzCF,EAAA,CAAAwB,UAAA,IAAAyG,4BAAA,kBAwDM;QAGNjI,EAAA,CAAAwB,UAAA,IAAA0G,4BAAA,iBAGM;;;QA9DuBlI,EAAA,CAAAU,SAAA,GAAqB;QAArBV,EAAA,CAAAK,UAAA,SAAA2H,GAAA,CAAA1E,eAAA,CAAqB;QA2D5CtD,EAAA,CAAAU,SAAA,GAAsB;QAAtBV,EAAA,CAAAK,UAAA,UAAA2H,GAAA,CAAA1E,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}