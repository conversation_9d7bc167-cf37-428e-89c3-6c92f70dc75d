@use 'sass:map';
@use '../core/density/private/compatibility';
@use '../core/theming/theming';
@use '../core/typography/typography';
@use '../core/typography/typography-utils';
@use './tree-variables';

@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);
  $background: map.get($config, background);
  $foreground: map.get($config, foreground);

  .mat-tree {
    background: theming.get-color-from-palette($background, 'card');
  }

  .mat-tree-node,
  .mat-nested-tree-node {
    color: theming.get-color-from-palette($foreground, text);
  }
}

@mixin typography($config-or-theme) {
  $config: typography.private-typography-to-2014-config(
      theming.get-typography-config($config-or-theme));
  .mat-tree {
    font-family: typography-utils.font-family($config);
  }

  .mat-tree-node,
  .mat-nested-tree-node {
    font-weight: typography-utils.font-weight($config, body-1);
    font-size: typography-utils.font-size($config, body-1);
  }
}

@mixin density($config-or-theme) {
  $density-scale: theming.get-density-config($config-or-theme);
  $height: compatibility.private-density-prop-value(tree-variables.$density-config,
    $density-scale, height);

  @include compatibility.private-density-legacy-compatibility() {
    .mat-tree-node {
      min-height: $height;
    }
  }
}

@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-tree') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}

