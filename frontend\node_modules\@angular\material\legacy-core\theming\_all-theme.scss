@use '../../core/theming/theming';
@use '../core-theme';
@use '../../legacy-button/button-theme';
@use '../../legacy-card/card-theme';
@use '../../legacy-progress-bar/progress-bar-theme';
@use '../../legacy-progress-spinner/progress-spinner-theme';
@use '../../legacy-tooltip/tooltip-theme';
@use '../../legacy-input/input-theme';
@use '../../legacy-form-field/form-field-theme';
@use '../../legacy-checkbox/checkbox-theme';
@use '../../legacy-select/select-theme';
@use '../../legacy-autocomplete/autocomplete-theme';
@use '../../legacy-dialog/dialog-theme';
@use '../../legacy-chips/chips-theme';
@use '../../legacy-slide-toggle/slide-toggle-theme';
@use '../../legacy-radio/radio-theme';
@use '../../legacy-slider/slider-theme';
@use '../../legacy-menu/menu-theme';
@use '../../legacy-list/list-theme';
@use '../../legacy-paginator/paginator-theme';
@use '../../legacy-tabs/tabs-theme';
@use '../../legacy-snack-bar/snack-bar-theme';
@use '../../legacy-table/table-theme';
@use '../../badge/badge-theme';
@use '../../bottom-sheet/bottom-sheet-theme';
@use '../../button-toggle/button-toggle-theme';
@use '../../datepicker/datepicker-theme';
@use '../../divider/divider-theme';
@use '../../expansion/expansion-theme';
@use '../../grid-list/grid-list-theme';
@use '../../icon/icon-theme';
@use '../../sidenav/sidenav-theme';
@use '../../stepper/stepper-theme';
@use '../../sort/sort-theme';
@use '../../toolbar/toolbar-theme';
@use '../../tree/tree-theme';

// Create a theme.
/// @deprecated Use `mat.all-component-themes` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin all-legacy-component-themes($theme-or-color-config) {
  $dedupe-key: 'angular-material-legacy-theme';
  @include theming.private-check-duplicate-theme-styles($theme-or-color-config, $dedupe-key) {
    // Legacy components.
    @include button-theme.theme($theme-or-color-config);
    @include core-theme.theme($theme-or-color-config);
    @include card-theme.theme($theme-or-color-config);
    @include progress-bar-theme.theme($theme-or-color-config);
    @include progress-spinner-theme.theme($theme-or-color-config);
    @include tooltip-theme.theme($theme-or-color-config);
    @include input-theme.theme($theme-or-color-config);
    @include form-field-theme.theme($theme-or-color-config);
    @include select-theme.theme($theme-or-color-config);
    @include checkbox-theme.theme($theme-or-color-config);
    @include autocomplete-theme.theme($theme-or-color-config);
    @include dialog-theme.theme($theme-or-color-config);
    @include chips-theme.theme($theme-or-color-config);
    @include slide-toggle-theme.theme($theme-or-color-config);
    @include radio-theme.theme($theme-or-color-config);
    @include slider-theme.theme($theme-or-color-config);
    @include menu-theme.theme($theme-or-color-config);
    @include list-theme.theme($theme-or-color-config);
    @include paginator-theme.theme($theme-or-color-config);
    @include tabs-theme.theme($theme-or-color-config);
    @include snack-bar-theme.theme($theme-or-color-config);
    @include table-theme.theme($theme-or-color-config);

    // Components without MDC versions.
    @include badge-theme.theme($theme-or-color-config);
    @include bottom-sheet-theme.theme($theme-or-color-config);
    @include button-toggle-theme.theme($theme-or-color-config);
    @include datepicker-theme.theme($theme-or-color-config);
    @include divider-theme.theme($theme-or-color-config);
    @include expansion-theme.theme($theme-or-color-config);
    @include grid-list-theme.theme($theme-or-color-config);
    @include icon-theme.theme($theme-or-color-config);
    @include sidenav-theme.theme($theme-or-color-config);
    @include stepper-theme.theme($theme-or-color-config);
    @include sort-theme.theme($theme-or-color-config);
    @include toolbar-theme.theme($theme-or-color-config);
    @include tree-theme.theme($theme-or-color-config);
  }
}

/// @deprecated Use `mat.all-component-themes` instead. See https://material.angular.io/guide/mdc-migration for information about migrating.
/// @breaking-change 17.0.0
@mixin angular-material-legacy-theme($theme-or-color-config) {
  @include all-legacy-component-themes($theme-or-color-config);
}
