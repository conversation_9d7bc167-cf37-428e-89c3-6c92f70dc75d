@use 'sass:map';
@use '@material/switch/switch-theme' as mdc-switch-theme;
@use '@material/form-field' as mdc-form-field;
@use '../core/theming/theming';
@use '../core/mdc-helpers/mdc-helpers';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/switch' as m2-mdc-switch;
@use '../core/tokens/m2/mat/slide-toggle' as m2-mat-slide-toggle;
@use '../core/tokens/token-utils';

@mixin color($config-or-theme) {
  $config: theming.get-color-config($config-or-theme);
  $primary: map.get($config, primary);
  $accent: map.get($config, accent);
  $warn: map.get($config, warn);
  $is-dark: map.get($config, is-dark);
  $foreground: map.get($config, foreground);

  $mdc-switch-color-tokens: m2-mdc-switch.get-color-tokens($config);

  @include mdc-helpers.using-mdc-theme($config) {
    // Add values for MDC slide toggles tokens
    .mat-mdc-slide-toggle {
      @include mdc-form-field.core-styles($query: mdc-helpers.$mdc-theme-styles-query);
      @include mdc-switch-theme.theme($mdc-switch-color-tokens);

      // MDC should set the disabled color on the label, but doesn't, so we do it here instead.
      .mdc-switch--disabled + label {
        color: theming.get-color-from-palette($foreground, disabled-text);
      }

      // Change the color palette related tokens to accent or warn if applicable
      &.mat-accent {
        @include mdc-switch-theme.theme(m2-mdc-switch.private-get-color-palette-color-tokens(
          map.get($config, accent),
          map.get($config, is-dark)
        ));
      }

      &.mat-warn {
        @include mdc-switch-theme.theme(m2-mdc-switch.private-get-color-palette-color-tokens(
          map.get($config, warn),
          map.get($config, is-dark)
        ));
      }
    }
  }
}

@mixin typography($config-or-theme) {
  $config: typography.private-typography-to-2018-config(
      theming.get-typography-config($config-or-theme));
  $mdc-switch-typography-tokens: m2-mdc-switch.get-typography-tokens($config);
  $mat-slide-toggle-typography-tokens: m2-mat-slide-toggle.get-typography-tokens($config);

  //Add values for MDC slide toggle tokens
  .mat-mdc-slide-toggle {
    @include mdc-form-field.core-styles($query: mdc-helpers.$mdc-typography-styles-query);
    @include mdc-switch-theme.theme($mdc-switch-typography-tokens);
    @include token-utils.create-token-values(
      m2-mat-slide-toggle.$prefix,
      $mat-slide-toggle-typography-tokens
    );
  }
}

@mixin density($config-or-theme) {
  $density-scale: theming.get-density-config($config-or-theme);
  .mat-mdc-slide-toggle {
    @include mdc-switch-theme.theme(mdc-switch-theme.density($density-scale));
  }
}

@mixin theme($theme-or-color-config) {
  $theme: theming.private-legacy-get-theme($theme-or-color-config);

  @include theming.private-check-duplicate-theme-styles($theme, 'mat-slide-toggle') {
    $color: theming.get-color-config($theme);
    $density: theming.get-density-config($theme);
    $typography: theming.get-typography-config($theme);

    @if $color != null {
      @include color($color);
    }
    @if $density != null {
      @include density($density);
    }
    @if $typography != null {
      @include typography($typography);
    }
  }
}
