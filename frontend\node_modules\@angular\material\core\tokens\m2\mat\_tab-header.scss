@use 'sass:map';
@use '../../token-utils';
@use '../../../theming/theming';
@use '../../../typography/typography-utils';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, tab-header);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($config) {
  $is-dark: map.get($config, is-dark);
  $foreground: map.get($config, foreground);
  $primary: map.get($config, primary);
  $inactive-label-text-color: rgba(if($is-dark, #fff, #000), 0.6);
  $active-label-text-color: theming.get-color-from-palette($primary, default);
  $ripple-color: theming.get-color-from-palette($primary, default);

  @return (
    disabled-ripple-color: theming.get-color-from-palette($foreground, disabled),
    pagination-icon-color: if($is-dark, #fff, #000),

    // Note: MDC has equivalents of these tokens, but they lead to much higher selector specificity.
    inactive-label-text-color: $inactive-label-text-color,
    active-label-text-color: $active-label-text-color,

    // Tokens needed to implement the gmat styles. Externally they don't change.
    active-ripple-color: $ripple-color,
    inactive-ripple-color: $ripple-color,
    inactive-focus-label-text-color: $inactive-label-text-color,
    inactive-hover-label-text-color: $inactive-label-text-color,
    active-focus-label-text-color: $active-label-text-color,
    active-hover-label-text-color: $active-label-text-color,
    active-focus-indicator-color: $active-label-text-color,
    active-hover-indicator-color: $active-label-text-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($config) {
  @return (
    // Note: MDC has equivalents of these tokens, but they lead to much higher selector specificity.
    label-text-font:
      typography-utils.font-family($config, button) or typography-utils.font-family($config),
    label-text-size: typography-utils.font-size($config, button),
    label-text-tracking: typography-utils.letter-spacing($config, button),
    label-text-line-height: typography-utils.line-height($config, button),
    label-text-weight: typography-utils.font-weight($config, button),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($config) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
          get-unthemable-tokens(),
          get-color-tokens(token-utils.$placeholder-color-config),
          get-typography-tokens(token-utils.$placeholder-typography-config),
          get-density-tokens(token-utils.$placeholder-density-config)
  );
}
