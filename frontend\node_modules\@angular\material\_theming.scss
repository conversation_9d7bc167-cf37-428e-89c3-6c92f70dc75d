// Forwards all public API mixins so they can be imported from a single entry point.
// Note that we have to forward the `.import` files for backwards-compatibility with
// projects that don't use Sass modules and include the `mat-`-prefixed mixins.

@forward '../cdk/a11y/index.import';
@forward '../cdk/overlay/index.import';
@forward '../cdk/text-field/index.import';

@forward './core/core-legacy-index';
@forward './legacy-autocomplete/autocomplete-legacy-index';
@forward './badge/badge-legacy-index';
@forward './bottom-sheet/bottom-sheet-legacy-index';
@forward './button-toggle/button-toggle-legacy-index';
@forward './legacy-button/button-legacy-index';
@forward './legacy-card/card-legacy-index';
@forward './legacy-checkbox/checkbox-legacy-index';
@forward './legacy-chips/chips-legacy-index';
@forward './datepicker/datepicker-legacy-index';
@forward './legacy-dialog/dialog-legacy-index';
@forward './divider/divider-legacy-index';
@forward './expansion/expansion-legacy-index';
@forward './legacy-form-field/form-field-legacy-index';
@forward './grid-list/grid-list-legacy-index';
@forward './icon/icon-legacy-index';
@forward './legacy-input/input-legacy-index';
@forward './legacy-list/list-legacy-index';
@forward './legacy-menu/menu-legacy-index';
@forward './legacy-paginator/paginator-legacy-index';
@forward './legacy-progress-bar/progress-bar-legacy-index';
@forward './legacy-progress-spinner/progress-spinner-legacy-index';
@forward './legacy-radio/radio-legacy-index';
@forward './legacy-select/select-legacy-index';
@forward './sidenav/sidenav-legacy-index';
@forward './legacy-slide-toggle/slide-toggle-legacy-index';
@forward './legacy-slider/slider-legacy-index';
@forward './legacy-snack-bar/snack-bar-legacy-index';
@forward './sort/sort-legacy-index';
@forward './stepper/stepper-legacy-index';
@forward './legacy-table/table-legacy-index';
@forward './legacy-tabs/tabs-legacy-index';
@forward './toolbar/toolbar-legacy-index';
@forward './legacy-tooltip/tooltip-legacy-index';
@forward './tree/tree-legacy-index';
